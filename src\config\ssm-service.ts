import { SSMClient, GetParametersCommand, GetParametersByPathCommand } from '@aws-sdk/client-ssm';
import { getAWSSDKConfig } from './aws-config';

// Types for our configuration
export interface SSMParameter {
  name: string;
  value: string;
  type: string;
}

export interface SSMServiceConfig {
  region?: string;
  parameterPrefix?: string;
  cacheTTL?: number;
  maxRetries?: number;
  retryDelay?: number;
}

// Cache interface
interface CacheEntry {
  value: string;
  timestamp: number;
  ttl: number;
}

class SSMParameterService {
  private client: SSMClient;
  private cache: Map<string, CacheEntry> = new Map();
  private config: Required<SSMServiceConfig>;
  private isInitialized = false;

  constructor(config: SSMServiceConfig = {}) {
    const awsConfig = getAWSSDKConfig();

    this.config = {
      region: config.region || awsConfig.region,
      parameterPrefix: config.parameterPrefix || '/maruti_site/env/',
      cacheTTL: config.cacheTTL || 300000, // 5 minutes
      maxRetries: config.maxRetries || 3,
      retryDelay: config.retryDelay || 1000, // 1 second
    };

    // Initialize AWS SSM Client with centralized AWS configuration
    this.client = new SSMClient({
      region: this.config.region,
      credentials: awsConfig.credentials,
      maxAttempts: this.config.maxRetries,
      retryMode: awsConfig.retryMode,
    });
  }

  private isValidCacheEntry(entry: CacheEntry): boolean {
    return Date.now() - entry.timestamp < entry.ttl;
  }

  private async sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  private async retryOperation<T>(
    operation: () => Promise<T>,
    retries: number = this.config.maxRetries
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error as Error;

        if (attempt < retries) {
          const delay = this.config.retryDelay * Math.pow(2, attempt); // Exponential backoff
          console.warn(`SSM operation failed (attempt ${attempt + 1}/${retries + 1}), retrying in ${delay}ms:`, error);
          await this.sleep(delay);
        }
      }
    }

    throw lastError!;
  }

  /**
   * Get a single parameter from SSM Parameter Store
   */
  async getParameter(parameterName: string, useCache: boolean = true): Promise<string | null> {
    const fullParameterName = parameterName.startsWith('/')
      ? parameterName
      : `${this.config.parameterPrefix}${parameterName}`;

    // Check cache first
    if (useCache) {
      const cached = this.cache.get(fullParameterName);
      if (cached && this.isValidCacheEntry(cached)) {
        return cached.value;
      }
    }

    try {
      const result = await this.retryOperation(async () => {
        const command = new GetParametersCommand({
          Names: [fullParameterName],
          WithDecryption: true,
        });

        return await this.client.send(command);
      });

      if (result.Parameters && result.Parameters.length > 0) {
        const parameter = result.Parameters[0];
        const value = parameter.Value || '';

        // Cache the result
        if (useCache) {
          this.cache.set(fullParameterName, {
            value,
            timestamp: Date.now(),
            ttl: this.config.cacheTTL,
          });
        }

        return value;
      }

      // Parameter not found
      if (result.InvalidParameters && result.InvalidParameters.length > 0) {
        console.warn(`SSM Parameter not found: ${fullParameterName}`);
      }

      return null;
    } catch (error) {
      console.error(`Failed to get SSM parameter ${fullParameterName}:`, error);
      return null;
    }
  }

  /**
   * Get multiple parameters from SSM Parameter Store
   */
  async getParameters(parameterNames: string[], useCache: boolean = true): Promise<Record<string, string | null>> {
    const results: Record<string, string | null> = {};
    const uncachedParameters: string[] = [];

    // Check cache for each parameter
    for (const paramName of parameterNames) {
      const fullParameterName = paramName.startsWith('/')
        ? paramName
        : `${this.config.parameterPrefix}${paramName}`;

      if (useCache) {
        const cached = this.cache.get(fullParameterName);
        if (cached && this.isValidCacheEntry(cached)) {
          results[paramName] = cached.value;
          continue;
        }
      }

      uncachedParameters.push(fullParameterName);
    }

    // Fetch uncached parameters
    if (uncachedParameters.length > 0) {
      try {
        const result = await this.retryOperation(async () => {
          const command = new GetParametersCommand({
            Names: uncachedParameters,
            WithDecryption: true,
          });

          return await this.client.send(command);
        });

        // Process successful parameters
        if (result.Parameters) {
          for (const parameter of result.Parameters) {
            const value = parameter.Value || '';
            const originalName = parameterNames.find(name => {
              const fullName = name.startsWith('/') ? name : `${this.config.parameterPrefix}${name}`;
              return fullName === parameter.Name;
            });

            if (originalName) {
              results[originalName] = value;

              // Cache the result
              if (useCache) {
                this.cache.set(parameter.Name!, {
                  value,
                  timestamp: Date.now(),
                  ttl: this.config.cacheTTL,
                });
              }
            }
          }
        }

        // Handle invalid parameters
        if (result.InvalidParameters) {
          for (const invalidParam of result.InvalidParameters) {
            const originalName = parameterNames.find(name => {
              const fullName = name.startsWith('/') ? name : `${this.config.parameterPrefix}${name}`;
              return fullName === invalidParam;
            });

            if (originalName) {
              results[originalName] = null;
              console.warn(`SSM Parameter not found: ${invalidParam}`);
            }
          }
        }
      } catch (error) {
        console.error('Failed to get SSM parameters:', error);

        // Set all uncached parameters to null
        for (const paramName of parameterNames) {
          if (!(paramName in results)) {
            results[paramName] = null;
          }
        }
      }
    }

    return results;
  }

  /**
   * Get all parameters by path prefix
   */
  async getParametersByPath(path?: string, useCache: boolean = true): Promise<Record<string, string>> {
    const searchPath = path || this.config.parameterPrefix;
    const results: Record<string, string> = {};

    try {
      let nextToken: string | undefined;

      do {
        const result = await this.retryOperation(async () => {
          const command = new GetParametersByPathCommand({
            Path: searchPath,
            Recursive: true,
            WithDecryption: true,
            NextToken: nextToken,
          });

          return await this.client.send(command);
        });

        if (result.Parameters) {
          for (const parameter of result.Parameters) {
            if (parameter.Name && parameter.Value) {
              // Remove the path prefix to get the key name
              const key = parameter.Name.replace(searchPath, '').replace(/^\//, '');
              results[key] = parameter.Value;

              // Cache the result
              if (useCache) {
                this.cache.set(parameter.Name, {
                  value: parameter.Value,
                  timestamp: Date.now(),
                  ttl: this.config.cacheTTL,
                });
              }
            }
          }
        }

        nextToken = result.NextToken;
      } while (nextToken);

    } catch (error) {
      console.error(`Failed to get SSM parameters by path ${searchPath}:`, error);
    }

    return results;
  }

  /**
   * Clear the cache
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number; entries: string[] } {
    return {
      size: this.cache.size,
      entries: Array.from(this.cache.keys()),
    };
  }
}

// Create a singleton instance
export const ssmService = new SSMParameterService();

// Export the class for custom instances
export { SSMParameterService };
