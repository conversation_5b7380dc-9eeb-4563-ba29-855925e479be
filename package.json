{"name": "maruti-tech", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "postbuild": "next-sitemap", "start": "next start", "lint": "eslint --ext .tsx,.ts .", "lint:fix": "npm run lint -- --fix", "format": "prettier --check .", "format:fix": "prettier --write .", "prepare": "husky", "dev:storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@aws-sdk/client-ssm": "^3.835.0", "@aws-sdk/credential-providers": "^3.835.0", "@commitlint/config-conventional": "^19.2.2", "@docsearch/react": "^3.8.2", "algoliasearch": "^5.20.0", "axios": "^1.7.7", "bootstrap": "^5.3.3", "commitlint": "^19.3.0", "embla-carousel": "^8.1.3", "embla-carousel-auto-scroll": "^8.1.3", "embla-carousel-autoplay": "^8.1.3", "embla-carousel-fade": "^8.1.3", "embla-carousel-react": "^8.1.3", "next": "14.1.1", "next-sitemap": "^4.2.3", "react": "^18", "react-bootstrap": "^2.10.2", "react-cookie-consent": "^9.0.0", "react-countup": "^6.5.3", "react-dom": "^18", "react-gauge-component": "^1.2.64", "react-instantsearch": "^7.15.1", "react-paginate": "^8.2.0", "react-phone-input-2": "^2.15.1"}, "devDependencies": {"@storybook/addon-essentials": "^8.0.10", "@storybook/addon-interactions": "^8.0.10", "@storybook/addon-links": "^8.0.10", "@storybook/addon-onboarding": "^8.0.10", "@storybook/blocks": "^8.0.10", "@storybook/nextjs": "^8.0.10", "@storybook/react": "^8.0.10", "@storybook/test": "^8.0.10", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@typescript-eslint/eslint-plugin": "^7.1.1", "eslint": "^8", "eslint-config-airbnb": "^19.0.4", "eslint-config-next": "^14.1.2", "eslint-config-prettier": "^9.1.0", "eslint-import-resolver-alias": "^1.1.2", "eslint-plugin-css-modules": "^2.12.0", "eslint-plugin-filename-rules": "^1.3.1", "eslint-plugin-import": "^2.29.1", "eslint-plugin-simple-import-sort": "^12.1.0", "eslint-plugin-storybook": "^0.8.0", "husky": "^9.0.11", "lint-staged": "^15.2.2", "prettier": "^3.2.5", "storybook": "^8.0.10", "typescript": "^5"}, "lint-staged": {"*.{jsx,js,tsx,ts}": "eslint --cache --fix", "*.{js,jsx,ts,tsx,css,md}": "prettier --write"}}