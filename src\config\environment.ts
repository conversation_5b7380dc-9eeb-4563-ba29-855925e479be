import { ssmService } from './ssm-service';

// Define the environment variables we need to fetch from SSM
export const SSM_PARAMETER_MAPPING = {
  // HubSpot Configuration
  NEXT_PUBLIC_HUBSPOT_API_KEY: 'NEXT_PUBLIC_HUBSPOT_API_KEY',
  NEXT_PUBLIC_HUBSPOT_GET_IN_TOUCH_FORM_GUID: 'NEXT_PUBLIC_HUBSPOT_GET_IN_TOUCH_FORM_GUID',
  NEXT_PUBLIC_HUBSPOT_AI_READINESS_FORM_GUID: 'NEXT_PUBLIC_HUBSPOT_AI_READINESS_FORM_GUID',
  NEXT_PUBLIC_HUBSPOT_PORTAL_ID: 'NEXT_PUBLIC_HUBSPOT_PORTAL_ID',

  // Email Configuration
  NEXT_PUBLIC_MAIL_FROM: 'NEXT_PUBLIC_MAIL_FROM',
  NEXT_PUBLIC_MAIL_TO: 'NEXT_PUBLIC_MAIL_TO',

  // SendGrid Configuration
  NEXT_PUBLIC_SENDGRID_API_KEY: 'NEXT_PUBLIC_SENDGRID_API_KEY',
  NEXT_PUBLIC_SENDGRID_CONTACT_US_FORM_TEMPLATE: 'NEXT_PUBLIC_SENDGRID_CONTACT_US_FORM_TEMPLATE_ID',
  NEXT_PUBLIC_SENDGRID_AI_READINESS_FORM_TEMPLATE: 'NEXT_PUBLIC_SENDGRID_AI_READINESS_FORM_TEMPLATE_ID',
  NEXT_PUBLIC_SENDGRID_FAILURE_EMAIL_TEMPLATE_ID: 'NEXT_PUBLIC_SENDGRID_FAILURE_EMAIL_TEMPLATE_ID',

  // Slack Configuration
  NEXT_PUBLIC_SLACK_FAILURE_WEBHOOK_URL: 'NEXT_PUBLIC_SLACK_FAILURE_WEBHOOK_URL',
  NEXT_PUBLIC_SLACK_SUCCESS_WEBHOOK_URL: 'NEXT_PUBLIC_SLACK_SUCCESS_WEBHOOK_URL',
} as const;

// Type for our configuration keys
export type ConfigKey = keyof typeof SSM_PARAMETER_MAPPING;

// Interface for our application configuration
export interface AppConfig {
  // HubSpot
  hubspotApiKey: string | null;
  hubspotGetInTouchFormGuid: string | null;
  hubspotAiReadinessFormGuid: string | null;
  hubspotPortalId: string | null;

  // Email
  mailFrom: string | null;
  mailTo: string | null;

  // SendGrid
  sendgridApiKey: string | null;
  sendgridContactUsFormTemplate: string | null;
  sendgridAiReadinessFormTemplate: string | null;
  sendgridFailureEmailTemplateId: string | null;

  // Slack
  slackFailureWebhookUrl: string | null;
  slackSuccessWebhookUrl: string | null;
}

// Cache for configuration values
let configCache: AppConfig | null = null;
let cacheTimestamp: number = 0;
const CACHE_TTL = 300000; // 5 minutes

/**
 * Get a single configuration value with fallback to environment variables
 */
export async function getConfigValue(key: ConfigKey): Promise<string | null> {
  try {
    // Try to get from SSM first
    const ssmValue = await ssmService.getParameter(SSM_PARAMETER_MAPPING[key]);

    if (ssmValue !== null) {
      return ssmValue;
    }

    // Fallback to environment variable
    const envValue = process.env[key];
    if (envValue) {
      console.warn(`Using fallback environment variable for ${key}`);
      return envValue;
    }

    console.warn(`Configuration value not found for ${key} in SSM or environment variables`);
    return null;
  } catch (error) {
    console.error(`Error getting configuration value for ${key}:`, error);

    // Fallback to environment variable on error
    const envValue = process.env[key];
    if (envValue) {
      console.warn(`Using fallback environment variable for ${key} due to SSM error`);
      return envValue;
    }

    return null;
  }
}

/**
 * Get all configuration values at once
 */
export async function getAppConfig(forceRefresh: boolean = false): Promise<AppConfig> {
  // Check cache first
  if (!forceRefresh && configCache && (Date.now() - cacheTimestamp) < CACHE_TTL) {
    return configCache;
  }

  try {
    // Get all parameters from SSM
    const parameterNames = Object.values(SSM_PARAMETER_MAPPING);
    const ssmResults = await ssmService.getParameters(parameterNames);

    // Build configuration object with fallbacks
    const config: AppConfig = {
      hubspotApiKey: ssmResults[SSM_PARAMETER_MAPPING.NEXT_PUBLIC_HUBSPOT_API_KEY] ||
        process.env.NEXT_PUBLIC_HUBSPOT_API_KEY || null,

      hubspotGetInTouchFormGuid: ssmResults[SSM_PARAMETER_MAPPING.NEXT_PUBLIC_HUBSPOT_GET_IN_TOUCH_FORM_GUID] ||
        process.env.NEXT_PUBLIC_HUBSPOT_GET_IN_TOUCH_FORM_GUID || null,

      hubspotAiReadinessFormGuid: ssmResults[SSM_PARAMETER_MAPPING.NEXT_PUBLIC_HUBSPOT_AI_READINESS_FORM_GUID] ||
        process.env.NEXT_PUBLIC_HUBSPOT_AI_READINESS_FORM_GUID || null,

      hubspotPortalId: ssmResults[SSM_PARAMETER_MAPPING.NEXT_PUBLIC_HUBSPOT_PORTAL_ID] ||
        process.env.NEXT_PUBLIC_HUBSPOT_PORTAL_ID || null,

      mailFrom: ssmResults[SSM_PARAMETER_MAPPING.NEXT_PUBLIC_MAIL_FROM] ||
        process.env.NEXT_PUBLIC_MAIL_FROM || null,

      mailTo: ssmResults[SSM_PARAMETER_MAPPING.NEXT_PUBLIC_MAIL_TO] ||
        process.env.NEXT_PUBLIC_MAIL_TO || null,

      sendgridApiKey: ssmResults[SSM_PARAMETER_MAPPING.NEXT_PUBLIC_SENDGRID_API_KEY] ||
        process.env.NEXT_PUBLIC_SENDGRID_API_KEY || null,

      sendgridContactUsFormTemplate: ssmResults[SSM_PARAMETER_MAPPING.NEXT_PUBLIC_SENDGRID_CONTACT_US_FORM_TEMPLATE] ||
        process.env.NEXT_PUBLIC_SENDGRID_CONTACT_US_FORM_TEMPLATE_ID || null,

      sendgridAiReadinessFormTemplate: ssmResults[SSM_PARAMETER_MAPPING.NEXT_PUBLIC_SENDGRID_AI_READINESS_FORM_TEMPLATE] ||
        process.env.NEXT_PUBLIC_SENDGRID_AI_READINESS_FORM_TEMPLATE_ID || null,

      sendgridFailureEmailTemplateId: ssmResults[SSM_PARAMETER_MAPPING.NEXT_PUBLIC_SENDGRID_FAILURE_EMAIL_TEMPLATE_ID] ||
        process.env.NEXT_PUBLIC_SENDGRID_FAILURE_EMAIL_TEMPLATE_ID || null,

      slackFailureWebhookUrl: ssmResults[SSM_PARAMETER_MAPPING.NEXT_PUBLIC_SLACK_FAILURE_WEBHOOK_URL] ||
        process.env.NEXT_PUBLIC_SLACK_FAILURE_WEBHOOK_URL || null,

      slackSuccessWebhookUrl: ssmResults[SSM_PARAMETER_MAPPING.NEXT_PUBLIC_SLACK_SUCCESS_WEBHOOK_URL] ||
        process.env.NEXT_PUBLIC_SLACK_SUCCESS_WEBHOOK_URL || null,
    };

    // Log warnings for missing values
    Object.entries(config).forEach(([key, value]) => {
      if (value === null) {
        console.warn(`Configuration value is null for ${key}`);
      }
    });

    // Cache the configuration
    configCache = config;
    cacheTimestamp = Date.now();

    return config;
  } catch (error) {
    console.error('Error getting app configuration:', error);

    // Fallback to environment variables only
    const fallbackConfig: AppConfig = {
      hubspotApiKey: process.env.NEXT_PUBLIC_HUBSPOT_API_KEY || null,
      hubspotGetInTouchFormGuid: process.env.NEXT_PUBLIC_HUBSPOT_GET_IN_TOUCH_FORM_GUID || null,
      hubspotAiReadinessFormGuid: process.env.NEXT_PUBLIC_HUBSPOT_AI_READINESS_FORM_GUID || null,
      hubspotPortalId: process.env.NEXT_PUBLIC_HUBSPOT_PORTAL_ID || null,
      mailFrom: process.env.NEXT_PUBLIC_MAIL_FROM || null,
      mailTo: process.env.NEXT_PUBLIC_MAIL_TO || null,
      sendgridApiKey: process.env.NEXT_PUBLIC_SENDGRID_API_KEY || null,
      sendgridContactUsFormTemplate: process.env.NEXT_PUBLIC_SENDGRID_CONTACT_US_FORM_TEMPLATE_ID || null,
      sendgridAiReadinessFormTemplate: process.env.NEXT_PUBLIC_SENDGRID_AI_READINESS_FORM_TEMPLATE_ID || null,
      sendgridFailureEmailTemplateId: process.env.NEXT_PUBLIC_SENDGRID_FAILURE_EMAIL_TEMPLATE_ID || null,
      slackFailureWebhookUrl: process.env.NEXT_PUBLIC_SLACK_FAILURE_WEBHOOK_URL || null,
      slackSuccessWebhookUrl: process.env.NEXT_PUBLIC_SLACK_SUCCESS_WEBHOOK_URL || null,
    };

    console.warn('Using fallback environment variables due to SSM error');
    return fallbackConfig;
  }
}

/**
 * Clear the configuration cache
 */
export function clearConfigCache(): void {
  configCache = null;
  cacheTimestamp = 0;
}

/**
 * Validate that all required configuration values are present
 */
export function validateConfig(config: AppConfig): { isValid: boolean; missingKeys: string[] } {
  const requiredKeys: (keyof AppConfig)[] = [
    'hubspotApiKey',
    'hubspotGetInTouchFormGuid',
    'hubspotAiReadinessFormGuid',
    'hubspotPortalId',
    'mailFrom',
    'mailTo',
    'sendgridApiKey',
    'sendgridContactUsFormTemplate',
    'sendgridAiReadinessFormTemplate',
    'sendgridFailureEmailTemplateId',
    'slackFailureWebhookUrl',
    'slackSuccessWebhookUrl',
  ];

  const missingKeys = requiredKeys.filter(key => !config[key]);

  return {
    isValid: missingKeys.length === 0,
    missingKeys,
  };
}

/**
 * Initialize configuration and validate
 */
export async function initializeConfig(): Promise<{ config: AppConfig; validation: { isValid: boolean; missingKeys: string[] } }> {
  const config = await getAppConfig();
  const validation = validateConfig(config);

  if (!validation.isValid) {
    console.warn('Configuration validation failed. Missing keys:', validation.missingKeys);
  }

  return { config, validation };
}
