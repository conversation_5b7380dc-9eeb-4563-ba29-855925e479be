import { AwsCredentialIdentity, AwsCredentialIdentityProvider } from '@aws-sdk/types';
import { fromEnv, fromInstanceMetadata, fromContainerMetadata, fromIni } from '@aws-sdk/credential-providers';

export interface AWSConfig {
  region: string;
  credentials?: AwsCredentialIdentityProvider;
  maxAttempts?: number;
  retryMode?: string;
}

/**
 * AWS Configuration for different environments
 */
export class AWSConfigManager {
  private static instance: AWSConfigManager;
  private config: AWSConfig;

  private constructor() {
    this.config = this.initializeConfig();
  }

  public static getInstance(): AWSConfigManager {
    if (!AWSConfigManager.instance) {
      AWSConfigManager.instance = new AWSConfigManager();
    }
    return AWSConfigManager.instance;
  }

  private initializeConfig(): AWSConfig {
    const region = this.getRegion();
    const credentials = this.getCredentialProvider();

    return {
      region,
      credentials,
      maxAttempts: 3,
      retryMode: 'adaptive',
    };
  }

  private getRegion(): string {
    // Priority order for region detection:
    // 1. AWS_REGION environment variable
    // 2. AWS_DEFAULT_REGION environment variable  
    // 3. Default to us-east-1
    return (
      process.env.AWS_REGION ||
      process.env.AWS_DEFAULT_REGION ||
      'us-east-1'
    );
  }

  private getCredentialProvider(): AwsCredentialIdentityProvider {
    const environment = process.env.NODE_ENV || 'development';
    
    console.log(`Initializing AWS credentials for environment: ${environment}`);

    // For production environments, try multiple credential sources
    if (environment === 'production') {
      return this.getProductionCredentials();
    }
    
    // For development/staging, prefer environment variables
    return this.getDevelopmentCredentials();
  }

  private getProductionCredentials(): AwsCredentialIdentityProvider {
    // Production credential chain (in order of preference):
    // 1. Environment variables (for containers with injected credentials)
    // 2. ECS Container Metadata (for ECS tasks with task roles)
    // 3. EC2 Instance Metadata (for EC2 instances with instance profiles)
    // 4. Shared credentials file (fallback)

    return async (): Promise<AwsCredentialIdentity> => {
      const credentialProviders = [
        { name: 'Environment Variables', provider: fromEnv },
        { name: 'ECS Container Metadata', provider: fromContainerMetadata },
        { name: 'EC2 Instance Metadata', provider: fromInstanceMetadata },
        { name: 'Shared Credentials File', provider: () => fromIni() },
      ];

      for (const { name, provider } of credentialProviders) {
        try {
          console.log(`Attempting to load credentials from: ${name}`);
          const credentialProvider = provider();
          const credentials = await credentialProvider();
          console.log(`Successfully loaded credentials from: ${name}`);
          return credentials;
        } catch (error) {
          console.warn(`Failed to load credentials from ${name}:`, error);
        }
      }

      throw new Error('Unable to load AWS credentials from any source');
    };
  }

  private getDevelopmentCredentials(): AwsCredentialIdentityProvider {
    // Development credential chain (in order of preference):
    // 1. Environment variables (AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY)
    // 2. Shared credentials file (~/.aws/credentials)
    // 3. Instance metadata (for development on EC2)

    return async (): Promise<AwsCredentialIdentity> => {
      const credentialProviders = [
        { name: 'Environment Variables', provider: fromEnv },
        { name: 'Shared Credentials File', provider: () => fromIni() },
        { name: 'Instance Metadata', provider: fromInstanceMetadata },
      ];

      for (const { name, provider } of credentialProviders) {
        try {
          console.log(`Attempting to load credentials from: ${name}`);
          const credentialProvider = provider();
          const credentials = await credentialProvider();
          console.log(`Successfully loaded credentials from: ${name}`);
          return credentials;
        } catch (error) {
          console.warn(`Failed to load credentials from ${name}:`, error);
        }
      }

      throw new Error('Unable to load AWS credentials from any source');
    };
  }

  public getConfig(): AWSConfig {
    return { ...this.config };
  }

  public updateRegion(region: string): void {
    this.config.region = region;
  }

  public async validateCredentials(): Promise<boolean> {
    try {
      if (!this.config.credentials) {
        return false;
      }

      const credentials = await this.config.credentials();
      return !!(credentials.accessKeyId && credentials.secretAccessKey);
    } catch (error) {
      console.error('Failed to validate AWS credentials:', error);
      return false;
    }
  }

  public async getCredentialInfo(): Promise<{
    hasCredentials: boolean;
    region: string;
    accessKeyId?: string;
    sessionToken?: boolean;
  }> {
    try {
      if (!this.config.credentials) {
        return {
          hasCredentials: false,
          region: this.config.region,
        };
      }

      const credentials = await this.config.credentials();
      return {
        hasCredentials: true,
        region: this.config.region,
        accessKeyId: credentials.accessKeyId ? `${credentials.accessKeyId.substring(0, 4)}****` : undefined,
        sessionToken: !!credentials.sessionToken,
      };
    } catch (error) {
      console.error('Failed to get credential info:', error);
      return {
        hasCredentials: false,
        region: this.config.region,
      };
    }
  }
}

// Export singleton instance
export const awsConfig = AWSConfigManager.getInstance();

// Helper function to get AWS config for SDK clients
export function getAWSSDKConfig(): AWSConfig {
  return awsConfig.getConfig();
}

// Helper function to validate AWS setup
export async function validateAWSSetup(): Promise<{
  isValid: boolean;
  region: string;
  credentialInfo: any;
  errors: string[];
}> {
  const errors: string[] = [];
  const config = awsConfig.getConfig();
  
  try {
    const credentialInfo = await awsConfig.getCredentialInfo();
    const hasValidCredentials = await awsConfig.validateCredentials();
    
    if (!hasValidCredentials) {
      errors.push('Invalid or missing AWS credentials');
    }
    
    if (!config.region) {
      errors.push('AWS region not configured');
    }
    
    return {
      isValid: errors.length === 0,
      region: config.region,
      credentialInfo,
      errors,
    };
  } catch (error) {
    errors.push(`AWS setup validation failed: ${error}`);
    return {
      isValid: false,
      region: config.region,
      credentialInfo: { hasCredentials: false, region: config.region },
      errors,
    };
  }
}

// Environment-specific configuration helpers
export const AWS_ENVIRONMENTS = {
  DEVELOPMENT: 'development',
  STAGING: 'staging', 
  PRODUCTION: 'production',
} as const;

export function getCurrentEnvironment(): string {
  return process.env.NODE_ENV || AWS_ENVIRONMENTS.DEVELOPMENT;
}

export function isProduction(): boolean {
  return getCurrentEnvironment() === AWS_ENVIRONMENTS.PRODUCTION;
}

export function isDevelopment(): boolean {
  return getCurrentEnvironment() === AWS_ENVIRONMENTS.DEVELOPMENT;
}
