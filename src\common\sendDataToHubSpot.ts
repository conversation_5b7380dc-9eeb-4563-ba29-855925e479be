const axios = require('axios');

const sendDataToHubspot = async (formPage, payload, formGuid, portalId = null) => {
  try {
    // Use provided portalId or fallback to environment variable
    const hubspotPortalId = portalId || process.env.NEXT_PUBLIC_HUBSPOT_PORTAL_ID;

    if (!hubspotPortalId) {
      throw new Error('HubSpot Portal ID is not configured');
    }

    const response = await axios.post(
      `https://api.hsforms.com/submissions/v3/integration/submit/${hubspotPortalId}/${formGuid}`,
      payload,
    );

    return {
      status: response.status, // Return actual HTTP status code
      message: `${formPage} form data sent to HubSpot successfully.`,
    };
  } catch (error) {
    return {
      status: error.response?.status || 500, // Return error status if available
      error: `${formPage} error while sending data to HubSpot.`,
    };
  }
};

export default sendDataToHubspot;
